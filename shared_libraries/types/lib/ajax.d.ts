export declare enum AjaxMethod {
    GET = "GET",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE"
}
export interface IRequestParam {
    method: AjaxMethod;
    contentType?: string;
    nocache?: boolean;
    queryParams?: Record<string, string | number>;
    accessToken?: string;
    timeout?: number;
    authorizationHeader?: string;
    responseType?: string;
    block?: boolean;
}
//# sourceMappingURL=ajax.d.ts.map