{"name": "@moxo/sdk", "version": "0.0.1", "main": "lib/index.js", "module": "es/index.js", "type": "module", "private": true, "types": "types/index.d.ts", "exports": {"./network": "./lib/network.js"}, "files": ["lib", "es", "types"], "sideEffects": false, "license": "MIT", "scripts": {"build": "rm -rf lib/ && tsc -p tsconfig.json", "test": "cross-env NODE_ENV=test jest --config .jest.js"}, "devDependencies": {"@types/lodash": "^4.14.165", "@types/superagent": "^8.1.9", "@moxo/tsconfig": "workspace:*", "lodash": "^4.17.15", "typescript": "^5.8.3"}, "dependencies": {"protobufjs": "^7.5.0", "superagent": "^10.2.2", "@moxo/proto": "workspace:*", "@moxo/types": "workspace:*", "@moxo/shared": "workspace:*"}, "description": "moxo sdk"}