import superagent from 'superagent';
import { uuid, getCookie } from '@moxo/shared';
import sdkConfig from '../common/config';

export declare enum AjaxMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
}
export interface IRequestParam {
  method: AjaxMethod;
  contentType?: string;
  nocache?: boolean;
  queryParams?: Record<string, string | number>;
  accessToken?: string;
  timeout?: number;
  authorizationHeader?: string;
  responseType?: string;
  block?: boolean;
}
export class RequestPromise<T> extends Promise<T> {
  public readonly requestId: string;

  constructor(
    executor: (
      resolve: (value: T | PromiseLike<T>) => void,
      reject: (reason?: any) => void,
    ) => void,
    requestId: string,
  ) {
    super(executor);
    this.requestId = requestId;
  }

  static fromPromise<T>(promise: Promise<T>, requestId: string): RequestPromise<T> {
    return new RequestPromise<T>((resolve, reject) => {
      promise.then(resolve, reject);
    }, requestId);
  }
}

// 存储所有活跃的请求
const activeRequests = new Map<string, superagent.SuperAgentRequest>();

// 维护阻塞请求链
let blockingPromise = Promise.resolve(null as unknown);

function createRequest(
  url: string,
  opts: IRequestParam,
  body?: unknown | Map<string, Blob>,
): RequestPromise<unknown> {
  const requestId = uuid(); // 生成唯一请求ID
  const req = superagent(opts.method, url);

  // 存储请求以便后续取消
  activeRequests.set(requestId, req);

  // 设置请求参数（与之前相同）
  req.set('Content-Type', opts.contentType || 'application/json');
  if (opts.contentType === 'application/x-protobuf') {
    req.set('Accept', 'application/x-protobuf');
    req.responseType('arraybuffer');
  }
  if (opts.responseType) {
    req.responseType(opts.responseType);
  }

  if (opts.nocache) {
    req.set('Cache-Control', 'no-cache');
  }
  const params = opts.queryParams || {};
  params['rnd'] = uuid();
  req.query(params);

  // 授权头设置（与之前相同）
  if (opts.authorizationHeader) {
    req.set('Authorization', opts.authorizationHeader);
  } else if (opts.accessToken) {
    req.set('Authorization', 'Bearer ' + opts.accessToken);
  } else if (location.protocol === 'http:' && sdkConfig.accessToken) {
    req.set('Authorization', 'Bearer ' + sdkConfig.accessToken);
  }

  // 超时设置（与之前相同）
  const responeTimeout = opts.timeout ? opts.timeout : 30 * 1000;
  const deadlineTimeout = 2 * 60 * 1000;
  req.timeout({
    response: responeTimeout,
    deadline: deadlineTimeout,
  });

  // 其他设置（与之前相同）
  if (!sdkConfig.useCookies) {
    const userId = sdkConfig.userId;
    const userToken = sdkConfig.userToken;
    if (userId && userToken) {
      req.set('X-User-Id', userId);
      req.set('X-User-Token', userToken);
    }

    if (sdkConfig.sessionId) {
      req.set('X-Session-Id', sdkConfig.sessionId);
    }
  }

  if (sdkConfig.clientVersion) {
    req.set('Client-Version', sdkConfig.clientVersion);
  }

  const csrfToken = getCookie('XSRF-TOKEN');
  if (csrfToken) {
    req.set('X-XSRF-TOKEN', csrfToken);
  }

  if (body) {
    if (body instanceof Map) {
      body.forEach((blob, name) => {
        req.attach(name, blob);
      });
    } else {
      req.send(body);
    }
  }
  return new RequestPromise((resolve, reject) => {
    req.end((err, res) => {
      // 请求完成后从活跃请求中移除
      activeRequests.delete(requestId);

      if (err) {
        if (err.name !== 'AbortError') {
          reject(err);
        }
        // 如果是取消错误，不处理，让调用者自己处理
      } else {
        resolve(res.body);
      }
    });
  }, requestId);
}
/**
 * 发送网络请求（支持阻塞/非阻塞模式，可取消）
 * @param url 请求URL
 * @param opts 请求配置参数
 * @param body 请求体数据
 * @returns 带有requestId的Promise对象，可通过abortRequest取消
 *
 * @example
 * // 基本用法
 * const req = sendRequest('/api/data', { method: 'GET' });
 * req.then(data => console.log(data));
 *
 * // 阻塞请求（会等待前一个阻塞请求完成）
 * const req = sendRequest('/api/data', { method: 'GET', block: true });
 *
 * // 多内容上传
 * const body = new Map()
 * body.set('file1', blob)
 * body.set('file2', blob)
 * sendRequest('/api/data', { method: 'POST' },body)
 *
 * // 取消请求
 * abortRequest(req);
 */
export function sendRequest(
  url: string,
  opts: IRequestParam,
  body?: unknown | Map<string, Blob>,
): RequestPromise<unknown> {
  const originalRequest = createRequest(url, opts, body);

  if (opts.block) {
    // 阻塞请求：加入阻塞链，串行执行
    const prevPromise = blockingPromise;

    // 创建一个新的Promise，等待前一个阻塞请求完成后再执行当前请求
    const wrappedPromise = prevPromise.then(() => originalRequest).catch(() => originalRequest);

    // 更新阻塞链
    blockingPromise = wrappedPromise.catch(() => {}); // 确保链不会因为错误而中断

    // 返回包装后的RequestPromise，保持requestId
    return RequestPromise.fromPromise(wrappedPromise, originalRequest.requestId);
  } else {
    // 非阻塞请求：等待最后一个阻塞请求完成后并行执行
    const wrappedPromise = blockingPromise.then(
      () => originalRequest,
      () => originalRequest,
    );

    // 返回包装后的RequestPromise，保持requestId
    return RequestPromise.fromPromise(wrappedPromise, originalRequest.requestId);
  }
}

/**
 * 取消指定请求
 * @param request 由sendRequest返回的请求对象或请求ID
 */
export function abortRequest(request: string | RequestPromise<unknown>): void {
  const requestId = typeof request === 'string' ? request : request.requestId;
  if (!requestId) return;

  const req = activeRequests.get(requestId);
  if (req) {
    req.abort();
    activeRequests.delete(requestId);
  }
}
