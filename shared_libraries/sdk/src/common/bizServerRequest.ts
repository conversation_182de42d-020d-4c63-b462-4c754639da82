import { ClientRequest, ClientResponse, ClientResponseCode, ClientResponseDetailCode } from '@moxo/proto';
import { AjaxMethod, IRequestParam, sendRequest } from '../network/ajax';
import sdkConfig from './config';

/**
 * Business server error class that extends the standard Error
 * Contains server response information for better error handling
 */
export class BizServerError extends Error {
  public readonly code?: ClientResponseCode;
  public readonly detailCode?: ClientResponseDetailCode;
  public readonly serverMessage?: string;
  public readonly response?: ClientResponse;
  public readonly requestId?: string;

  constructor(
    message: string,
    code?: ClientResponseCode,
    detailCode?: ClientResponseDetailCode,
    serverMessage?: string,
    response?: ClientResponse,
    requestId?: string
  ) {
    super(message);
    this.name = 'BizServerError';
    this.code = code;
    this.detailCode = detailCode;
    this.serverMessage = serverMessage;
    this.response = response;
    this.requestId = requestId;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, BizServerError);
    }
  }
}

/**
 * Base RequestPromise class that extends Promise with requestId property
 */
export class RequestPromise<T> extends Promise<T> {
  public readonly requestId: string;

  constructor(
    executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void,
    requestId: string
  ) {
    super(executor);
    this.requestId = requestId;
  }

  static fromPromise<T>(promise: Promise<T> & { requestId: string }): RequestPromise<T> {
    return new RequestPromise<T>((resolve, reject) => {
      promise.then(resolve, reject);
    }, promise.requestId);
  }
}

/**
 * Business request promise that returns ClientResponse on success
 * and BizServerError on failure
 */
export class BizRequestPromise extends RequestPromise<ClientResponse> {
  constructor(
    executor: (resolve: (value: ClientResponse | PromiseLike<ClientResponse>) => void, reject: (reason?: BizServerError) => void) => void,
    requestId: string
  ) {
    super(executor, requestId);
  }

  static fromRequest(requestPromise: Promise<unknown> & { requestId: string }): BizRequestPromise {
    return new BizRequestPromise((resolve, reject) => {
      requestPromise
        .then((response) => {
          const clientResponse = response as ClientResponse;

          // Check if the response indicates an error
          const isSuccessResponse = !clientResponse.code ||
            clientResponse.code === ClientResponseCode.RESPONSE_SUCCESS ||
            clientResponse.code === ClientResponseCode.RESPONSE_ACCEPTED ||
            clientResponse.code === ClientResponseCode.RESPONSE_NO_CONTENT ||
            clientResponse.code === ClientResponseCode.RESPONSE_CONNECT_SUCCESS;

          if (!isSuccessResponse) {
            // Create BizServerError from server response
            const bizError = new BizServerError(
              clientResponse.message || `Server error: ${clientResponse.code}`,
              clientResponse.code,
              clientResponse.detail_code,
              clientResponse.message,
              clientResponse,
              requestPromise.requestId
            );
            reject(bizError);
          } else {
            resolve(clientResponse);
          }
        })
        .catch((error) => {
          // Convert any error to BizServerError
          if (error instanceof BizServerError) {
            reject(error);
          } else {
            // Create BizServerError from generic error
            const bizError = new BizServerError(
              error.message || 'Request failed',
              undefined,
              undefined,
              undefined,
              undefined,
              requestPromise.requestId
            );
            reject(bizError);
          }
        });
    }, requestPromise.requestId);
  }
}

export function sendBizServerRequest(
  body: ClientRequest,
  opts: IRequestParam,
): BizRequestPromise {
  let requestUrl = window.location.origin + sdkConfig.servicePath;
  if (body.object?.board) {
    requestUrl += '/board';
  } else if (body.object?.user) {
    requestUrl += '/user';
  } else if (body.object?.group) {
    requestUrl += '/group';
  }

  const requestPromise = sendRequest(requestUrl, { method: AjaxMethod.POST, ...opts }, body);
  return BizRequestPromise.fromRequest(requestPromise);
}
