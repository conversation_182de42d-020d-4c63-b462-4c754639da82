import { ClientRequest, ClientResponse, ClientResponseCode, ClientResponseDetailCode } from '@moxo/proto';
import { AjaxMethod, IRequestParam, sendRequest } from '../network/ajax';
import sdkConfig from './config';
import {
  IBizServerError,
  IRequestPromise,
  IBizRequestPromise,
  SUCCESS_RESPONSE_CODES,
  isSuccessResponse
} from './bizServerRequest.types';

/**
 * 业务服务器错误类，继承自标准 Error 类
 * 包含服务器响应信息，用于更好的错误处理
 * 实现 IBizServerError 接口
 */
export class BizServerError extends Error implements IBizServerError {
  public readonly code?: ClientResponseCode;
  public readonly detailCode?: ClientResponseDetailCode;
  public readonly serverMessage?: string;
  public readonly response?: ClientResponse;
  public readonly requestId?: string;

  constructor(
    message: string,
    code?: ClientResponseCode,
    detailCode?: ClientResponseDetailCode,
    serverMessage?: string,
    response?: ClientResponse,
    requestId?: string
  ) {
    super(message);
    this.name = 'BizServerError';
    this.code = code;
    this.detailCode = detailCode;
    this.serverMessage = serverMessage;
    this.response = response;
    this.requestId = requestId;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, BizServerError);
    }
  }
}

/**
 * 基础 RequestPromise 类，继承自 Promise 并添加 requestId 属性
 * 实现 IRequestPromise 接口
 */
export class RequestPromise<T> extends Promise<T> implements IRequestPromise<T> {
  public readonly requestId: string;

  constructor(
    executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void,
    requestId: string
  ) {
    super(executor);
    this.requestId = requestId;
  }

  static fromPromise<T>(promise: Promise<T> & { requestId: string }): RequestPromise<T> {
    return new RequestPromise<T>((resolve, reject) => {
      promise.then(resolve, reject);
    }, promise.requestId);
  }
}

/**
 * 业务请求 Promise 类，继承自 RequestPromise
 * 成功时返回 ClientResponse 对象，失败时返回 BizServerError 对象
 *
 * @example
 * ```typescript
 * const request = sendBizServerRequest(clientRequest, { method: AjaxMethod.POST });
 *
 * // 使用 async/await
 * try {
 *   const response = await request;
 *   console.log('成功:', response);
 * } catch (error) {
 *   if (error instanceof BizServerError) {
 *     console.error('业务错误:', error.message, error.code);
 *   }
 * }
 *
 * // 使用 Promise 链式调用
 * request
 *   .then(response => console.log('成功:', response))
 *   .catch(error => console.error('失败:', error));
 * ```
 */
export class BizRequestPromise extends RequestPromise<ClientResponse> implements IBizRequestPromise {
  constructor(
    executor: (
      resolve: (value: ClientResponse | PromiseLike<ClientResponse>) => void,
      reject: (reason?: BizServerError) => void
    ) => void,
    requestId: string
  ) {
    super(executor, requestId);
  }

  /**
   * 从普通的 RequestPromise 创建 BizRequestPromise
   * 自动处理响应转换和错误处理
   *
   * @param requestPromise 原始请求 Promise，必须包含 requestId 属性
   * @returns 返回 BizRequestPromise 实例
   */
  static fromRequest(requestPromise: Promise<unknown> & { requestId: string }): BizRequestPromise {
    return new BizRequestPromise((resolve, reject) => {
      requestPromise
        .then((response) => {
          const clientResponse = response as ClientResponse;

          // 检查响应是否表示成功
          const isSuccess = isSuccessResponse(clientResponse);

          if (!isSuccess) {
            // 从服务器响应创建 BizServerError
            const bizError = BizRequestPromise.createBizServerError(
              clientResponse,
              requestPromise.requestId
            );
            reject(bizError);
          } else {
            resolve(clientResponse);
          }
        })
        .catch((error) => {
          // 将任何错误转换为 BizServerError
          const bizError = BizRequestPromise.convertToBizServerError(
            error,
            requestPromise.requestId
          );
          reject(bizError);
        });
    }, requestPromise.requestId);
  }



  /**
   * 从服务器响应创建 BizServerError
   * @param response 客户端响应对象
   * @param requestId 请求ID
   * @returns BizServerError 实例
   */
  private static createBizServerError(
    response: ClientResponse,
    requestId: string
  ): BizServerError {
    return new BizServerError(
      response.message || `服务器错误: ${response.code}`,
      response.code,
      response.detail_code,
      response.message,
      response,
      requestId
    );
  }

  /**
   * 将普通错误转换为 BizServerError
   * @param error 原始错误对象
   * @param requestId 请求ID
   * @returns BizServerError 实例
   */
  private static convertToBizServerError(
    error: any,
    requestId: string
  ): BizServerError {
    if (error instanceof BizServerError) {
      return error;
    }

    return new BizServerError(
      error.message || '请求失败',
      undefined,
      undefined,
      undefined,
      undefined,
      requestId
    );
  }

  /**
   * 创建一个立即解析的 BizRequestPromise
   * @param response 响应数据
   * @param requestId 请求ID
   * @returns 立即解析的 BizRequestPromise
   */
  static resolve(response: ClientResponse, requestId: string = 'immediate'): BizRequestPromise {
    return new BizRequestPromise((resolve) => {
      resolve(response);
    }, requestId);
  }

  /**
   * 创建一个立即拒绝的 BizRequestPromise
   * @param error 错误对象
   * @param requestId 请求ID
   * @returns 立即拒绝的 BizRequestPromise
   */
  static reject(error: BizServerError, requestId: string = 'immediate'): BizRequestPromise {
    return new BizRequestPromise((_, reject) => {
      reject(error);
    }, requestId);
  }
}

export function sendBizServerRequest(
  body: ClientRequest,
  opts: IRequestParam,
): BizRequestPromise {
  let requestUrl = window.location.origin + sdkConfig.servicePath;
  if (body.object?.board) {
    requestUrl += '/board';
  } else if (body.object?.user) {
    requestUrl += '/user';
  } else if (body.object?.group) {
    requestUrl += '/group';
  }

  const requestPromise = sendRequest(requestUrl, { method: AjaxMethod.POST, ...opts }, body);
  return BizRequestPromise.fromRequest(requestPromise);
}
