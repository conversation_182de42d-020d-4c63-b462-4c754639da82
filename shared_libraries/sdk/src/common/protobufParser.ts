import * as protobuf from 'protobufjs';
import { ProtoBufDefineJSON } from '@moxo/proto/ProtoBufDefineJSON';
import { ClientRequest, ClientResponse } from '@moxo/proto';
const root = protobuf.Root.fromJSON(ProtoBufDefineJSON);
const requestParser: any = root.lookup('ClientRequest');
const responseParser: any = root.lookup('ClientResponse');

export function encodeProtobuf(json: ClientRequest) {
  let message = requestParser.fromObject(json);
  return requestParser.encode(message).finish();
}
export function decodeProtobuf(buffer: any): ClientResponse {
  let arrayBuf = new Uint8Array(buffer);
  return responseParser.toObject(responseParser.decode(arrayBuf), {
    enums: String,
    bytes: String,
    json: true,
  }) as ClientResponse;
}
