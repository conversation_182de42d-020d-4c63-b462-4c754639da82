import { ClientResponse, ClientResponseCode, ClientResponseDetailCode } from '@moxo/proto';
import { IRequestPromise } from '../network/ajax';

/**
 * BizRequestPromise 相关类型定义
 */

/**
 * 业务服务器错误接口
 */
export interface IBizServerError {
  /** 错误名称 */
  readonly name: string;
  /** 错误消息 */
  readonly message: string;
  /** 响应代码 */
  readonly code?: ClientResponseCode;
  /** 详细错误代码 */
  readonly detailCode?: ClientResponseDetailCode;
  /** 服务器返回的错误消息 */
  readonly serverMessage?: string;
  /** 完整的服务器响应 */
  readonly response?: ClientResponse;
  /** 请求ID */
  readonly requestId?: string;
}

/**
 * 业务请求 Promise 接口
 * 成功时返回 ClientResponse，失败时返回 BizServerError
 */
export interface IBizRequestPromise extends IRequestPromise<ClientResponse> {
  /** 请求的唯一标识符 */
  readonly requestId: string;
}

/**
 * 成功响应的状态码集合
 */
export const SUCCESS_RESPONSE_CODES = [
  ClientResponseCode.RESPONSE_SUCCESS,
  ClientResponseCode.RESPONSE_ACCEPTED,
  ClientResponseCode.RESPONSE_NO_CONTENT,
  ClientResponseCode.RESPONSE_CONNECT_SUCCESS,
] as const;

/**
 * 成功响应状态码类型
 */
export type SuccessResponseCode = (typeof SUCCESS_RESPONSE_CODES)[number];

/**
 * 请求状态枚举
 */
export enum RequestStatus {
  /** 等待中 */
  PENDING = 'pending',
  /** 进行中 */
  IN_PROGRESS = 'in_progress',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 已失败 */
  FAILED = 'failed',
  /** 已取消 */
  CANCELLED = 'cancelled',
}

/**
 * 请求回调函数类型
 */
export type RequestSuccessCallback = (response: ClientResponse) => void;
export type RequestErrorCallback = (error: IBizServerError) => void;
export type RequestCompleteCallback = (requestId: string) => void;

/**
 * 请求事件监听器接口
 */
export interface IRequestEventListeners {
  /** 请求成功回调 */
  onSuccess?: RequestSuccessCallback;
  /** 请求失败回调 */
  onError?: RequestErrorCallback;
  /** 请求完成回调（无论成功失败） */
  onComplete?: RequestCompleteCallback;
}

/**
 * 批量请求结果接口
 */
export interface IBatchRequestResult {
  /** 成功的响应列表 */
  successes: ClientResponse[];
  /** 失败的错误列表 */
  failures: IBizServerError[];
  /** 总请求数 */
  total: number;
  /** 成功数量 */
  successCount: number;
  /** 失败数量 */
  failureCount: number;
}

/**
 * 重试配置接口
 */
export interface IRetryConfig {
  /** 最大重试次数 */
  maxRetries: number;
  /** 重试延迟（毫秒） */
  retryDelay: number;
  /** 是否使用指数退避 */
  exponentialBackoff?: boolean;
  /** 重试条件判断函数 */
  shouldRetry?: (error: IBizServerError, attempt: number) => boolean;
}

/**
 * 请求缓存配置接口
 */
export interface ICacheConfig {
  /** 是否启用缓存 */
  enabled: boolean;
  /** 缓存过期时间（毫秒） */
  ttl: number;
  /** 缓存键生成函数 */
  keyGenerator?: (url: string, body: any) => string;
}

/**
 * 工具类型：提取 Promise 的解析类型
 */
export type PromiseResolveType<T> = T extends Promise<infer U> ? U : never;

/**
 * 工具类型：提取 Promise 的拒绝类型
 */
export type PromiseRejectType<T> = T extends Promise<any> ? any : never;

/**
 * 类型守卫：检查是否为 BizServerError
 */
export function isBizServerError(error: any): error is IBizServerError {
  return (
    error &&
    typeof error === 'object' &&
    error.name === 'BizServerError' &&
    typeof error.message === 'string'
  );
}

/**
 * 类型守卫：检查是否为成功响应
 */
export function isSuccessResponse(response: ClientResponse): boolean {
  if (!response.code) {
    return true;
  }
  return SUCCESS_RESPONSE_CODES.includes(response.code as SuccessResponseCode);
}
