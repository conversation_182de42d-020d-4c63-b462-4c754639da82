import { ClientRequest, ClientRequestType, ClientResponse, ClientResponseCode } from '@moxo/proto';
import { AjaxMethod } from '../network/ajax';
import { sendBizServerRequest, BizServerError, BizRequestPromise } from './bizServerRequest';

/**
 * BizRequestPromise 测试文件
 * 用于验证 BizRequestPromise 的各种功能
 */

// 模拟测试数据
const mockSuccessResponse: ClientResponse = {
  code: ClientResponseCode.RESPONSE_SUCCESS,
  message: '请求成功',
  sequence: 'test-sequence-success'
};

const mockErrorResponse: ClientResponse = {
  code: ClientResponseCode.RESPONSE_SERVER_ERROR,
  message: '服务器错误',
  sequence: 'test-sequence-error'
};

const mockRequest: ClientRequest = {
  type: ClientRequestType.USER_REQUEST_READ,
  sequence: 'test-request'
};

/**
 * 测试 BizRequestPromise 基本功能
 */
export async function testBasicFunctionality() {
  console.log('=== 测试 BizRequestPromise 基本功能 ===');

  try {
    // 测试静态方法 resolve
    console.log('1. 测试 BizRequestPromise.resolve()');
    const resolvedPromise = BizRequestPromise.resolve(mockSuccessResponse, 'test-resolve');
    console.log('Promise requestId:', resolvedPromise.requestId);
    
    const result = await resolvedPromise;
    console.log('✅ resolve 测试成功:', result.message);

    // 测试静态方法 reject
    console.log('\n2. 测试 BizRequestPromise.reject()');
    const rejectedPromise = BizRequestPromise.reject(
      new BizServerError('测试错误', ClientResponseCode.RESPONSE_SERVER_ERROR),
      'test-reject'
    );
    console.log('Promise requestId:', rejectedPromise.requestId);

    try {
      await rejectedPromise;
      console.log('❌ reject 测试失败: 应该抛出错误');
    } catch (error) {
      if (error instanceof BizServerError) {
        console.log('✅ reject 测试成功:', error.message);
      }
    }

  } catch (error) {
    console.error('❌ 基本功能测试失败:', error);
  }
}

/**
 * 测试类型安全性
 */
export async function testTypeSafety() {
  console.log('\n=== 测试类型安全性 ===');

  // 创建一个 BizRequestPromise
  const promise = BizRequestPromise.resolve(mockSuccessResponse, 'type-test');

  // 测试 Promise 链式调用的类型安全
  promise
    .then((response) => {
      // response 应该被推断为 ClientResponse 类型
      console.log('✅ 成功回调类型正确:', typeof response.code, typeof response.message);
      return response;
    })
    .catch((error) => {
      // error 应该被推断为 BizServerError 类型
      if (error instanceof BizServerError) {
        console.log('✅ 错误回调类型正确:', error.code, error.message);
      }
    });

  console.log('✅ 类型安全性测试完成');
}

/**
 * 测试继承关系
 */
export function testInheritance() {
  console.log('\n=== 测试继承关系 ===');

  const promise = BizRequestPromise.resolve(mockSuccessResponse, 'inheritance-test');

  // 测试是否正确继承
  console.log('是否为 Promise 实例:', promise instanceof Promise);
  console.log('是否为 BizRequestPromise 实例:', promise instanceof BizRequestPromise);
  console.log('是否有 requestId 属性:', 'requestId' in promise);
  console.log('requestId 值:', promise.requestId);

  if (promise instanceof Promise && 
      promise instanceof BizRequestPromise && 
      'requestId' in promise) {
    console.log('✅ 继承关系测试成功');
  } else {
    console.log('❌ 继承关系测试失败');
  }
}

/**
 * 测试错误处理
 */
export async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===');

  // 测试 BizServerError 的创建和属性
  const bizError = new BizServerError(
    '测试业务错误',
    ClientResponseCode.RESPONSE_SERVER_ERROR,
    undefined,
    '服务器内部错误',
    mockErrorResponse,
    'error-test-id'
  );

  console.log('BizServerError 属性测试:');
  console.log('- name:', bizError.name);
  console.log('- message:', bizError.message);
  console.log('- code:', bizError.code);
  console.log('- serverMessage:', bizError.serverMessage);
  console.log('- requestId:', bizError.requestId);
  console.log('- response:', bizError.response?.sequence);

  if (bizError.name === 'BizServerError' &&
      bizError.code === ClientResponseCode.RESPONSE_SERVER_ERROR &&
      bizError.requestId === 'error-test-id') {
    console.log('✅ 错误处理测试成功');
  } else {
    console.log('❌ 错误处理测试失败');
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行 BizRequestPromise 测试套件\n');

  try {
    await testBasicFunctionality();
    await testTypeSafety();
    testInheritance();
    await testErrorHandling();

    console.log('\n🎉 所有测试完成！');
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error);
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runAllTests();
}
