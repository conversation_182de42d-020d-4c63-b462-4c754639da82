import { ClientResponse, ClientResponseCode, ClientResponseDetailCode } from '@moxo/proto';

/**
 * Business server error class that extends the standard Error
 * Contains server response information for better error handling
 */
export class BizServerError extends Error {
  public readonly code?: ClientResponseCode;
  public readonly detailCode?: ClientResponseDetailCode;
  public readonly serverMessage?: string;
  public readonly response?: ClientResponse;
  public readonly requestId?: string;

  constructor(
    message: string,
    code?: ClientResponseCode,
    detailCode?: ClientResponseDetailCode,
    serverMessage?: string,
    response?: ClientResponse,
    requestId?: string,
  ) {
    super(message);
    this.name = 'BizServerError';
    this.code = code;
    this.detailCode = detailCode;
    this.serverMessage = serverMessage;
    this.response = response;
    this.requestId = requestId;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, BizServerError);
    }
  }
}
