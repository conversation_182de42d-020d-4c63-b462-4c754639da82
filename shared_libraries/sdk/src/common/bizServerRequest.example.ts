import { ClientRequest, ClientRequestType } from '@moxo/proto';
import { AjaxMethod } from '../network/ajax';
import { sendBizServerRequest, BizServerError, BizRequestPromise } from './bizServerRequest';

/**
 * BizRequestPromise 使用示例
 * 本文件演示如何使用新的 BizRequestPromise 类
 *
 * BizRequestPromise 特性：
 * 1. 继承自 RequestPromise<ClientResponse>
 * 2. 成功时返回 ClientResponse 对象
 * 3. 失败时返回 BizServerError 对象
 * 4. 支持 block 参数进行请求阻塞
 * 5. 包含 requestId 属性用于请求跟踪
 */

// Example 1: Basic usage with success handling
export async function exampleBasicUsage() {
  const request: ClientRequest = {
    type: ClientRequestType.USER_REQUEST_READ,
    sequence: 'example-sequence-1',
  };

  try {
    const response = await sendBizServerRequest(request, { method: AjaxMethod.POST });
    console.log('Success:', response);
    console.log('Request ID:', response.requestId); // Access to requestId property
    return response;
  } catch (error) {
    if (error instanceof BizServerError) {
      console.error('Business Server Error:', {
        message: error.message,
        code: error.code,
        detailCode: error.detailCode,
        serverMessage: error.serverMessage,
        requestId: error.requestId,
      });
    } else {
      console.error('Unexpected error:', error);
    }
    throw error;
  }
}

// Example 2: Using Promise.then/catch syntax
export function examplePromiseChaining() {
  const request: ClientRequest = {
    type: ClientRequestType.BOARD_REQUEST_READ,
    sequence: 'example-sequence-2',
  };

  return sendBizServerRequest(request, { method: AjaxMethod.POST })
    .then((response) => {
      console.log('Success response:', response);
      return response;
    })
    .catch((error: BizServerError) => {
      console.error('Error occurred:', error.message);
      if (error.code) {
        console.error('Server error code:', error.code);
      }
      throw error;
    });
}

// Example 3: Using with blocking requests
export async function exampleBlockingRequest() {
  const request: ClientRequest = {
    type: ClientRequestType.USER_REQUEST_UPDATE,
    sequence: 'example-sequence-3',
  };

  try {
    // This request will block subsequent requests until it completes
    const response = await sendBizServerRequest(request, { 
      method: AjaxMethod.POST, 
      block: true 
    });
    
    console.log('Blocking request completed:', response);
    return response;
  } catch (error) {
    if (error instanceof BizServerError) {
      console.error('Blocking request failed:', error.message);
    }
    throw error;
  }
}

// Example 4: Type-safe error handling
export function exampleTypeSafeErrorHandling(request: ClientRequest): BizRequestPromise {
  const promise = sendBizServerRequest(request, { method: AjaxMethod.POST });
  
  // The promise is typed to return ClientResponse on success
  // and BizServerError on failure
  promise
    .then((response) => {
      // response is typed as ClientResponse
      console.log('Response code:', response.code);
      console.log('Response message:', response.message);
    })
    .catch((error) => {
      // error is typed as BizServerError
      console.error('Error code:', error.code);
      console.error('Detail code:', error.detailCode);
      console.error('Server message:', error.serverMessage);
    });
    
  return promise;
}

// Example 5: Accessing requestId for request tracking
export function exampleRequestTracking() {
  const request: ClientRequest = {
    type: ClientRequestType.GROUP_REQUEST_READ,
    sequence: 'example-sequence-5',
  };

  const promise = sendBizServerRequest(request, { method: AjaxMethod.POST });

  // Access requestId immediately for tracking
  console.log('Started request with ID:', promise.requestId);

  return promise
    .then((response) => {
      console.log(`Request ${promise.requestId} completed successfully`);
      return response;
    })
    .catch((error: BizServerError) => {
      console.error(`Request ${promise.requestId} failed:`, error.message);
      throw error;
    });
}

// Example 6: 使用静态方法创建 Promise
export function exampleStaticMethods() {
  // 创建立即解析的 Promise
  const successPromise = BizRequestPromise.resolve({
    code: 200,
    message: '成功',
    sequence: 'test-sequence'
  }, 'static-success');

  // 创建立即拒绝的 Promise
  const errorPromise = BizRequestPromise.reject(
    new BizServerError('测试错误', 500, undefined, '服务器内部错误'),
    'static-error'
  );

  console.log('Success promise requestId:', successPromise.requestId);
  console.log('Error promise requestId:', errorPromise.requestId);

  return Promise.allSettled([successPromise, errorPromise]);
}

// Example 7: 批量请求处理
export async function exampleBatchRequests() {
  const requests: ClientRequest[] = [
    { type: ClientRequestType.USER_REQUEST_READ, sequence: 'batch-1' },
    { type: ClientRequestType.BOARD_REQUEST_READ, sequence: 'batch-2' },
    { type: ClientRequestType.GROUP_REQUEST_READ, sequence: 'batch-3' }
  ];

  // 创建所有请求的 Promise 数组
  const promises = requests.map(request =>
    sendBizServerRequest(request, { method: AjaxMethod.POST })
  );

  // 记录所有请求 ID
  const requestIds = promises.map(p => p.requestId);
  console.log('批量请求 IDs:', requestIds);

  try {
    // 等待所有请求完成
    const responses = await Promise.all(promises);
    console.log('所有请求成功完成:', responses.length);
    return responses;
  } catch (error) {
    if (error instanceof BizServerError) {
      console.error('批量请求中有失败:', error.requestId, error.message);
    }
    throw error;
  }
}

// Example 8: 请求重试机制
export async function exampleRetryMechanism(
  request: ClientRequest,
  maxRetries: number = 3
): Promise<any> {
  let lastError: BizServerError | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`尝试第 ${attempt} 次请求...`);

      const promise = sendBizServerRequest(request, {
        method: AjaxMethod.POST,
        timeout: 5000
      });

      console.log(`请求 ID: ${promise.requestId}`);

      const response = await promise;
      console.log(`第 ${attempt} 次请求成功`);
      return response;

    } catch (error) {
      lastError = error as BizServerError;
      console.error(`第 ${attempt} 次请求失败:`, lastError.message);

      if (attempt === maxRetries) {
        console.error('所有重试都失败了');
        throw lastError;
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }

  throw lastError;
}
