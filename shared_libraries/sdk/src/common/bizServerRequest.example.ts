import { ClientRequest, ClientRequestType } from '@moxo/proto';
import { AjaxMethod } from '../network/ajax';
import { sendBizServerRequest, BizServerError, BizRequestPromise } from './bizServerRequest';

/**
 * Example usage of BizRequestPromise
 * This file demonstrates how to use the new BizRequestPromise class
 */

// Example 1: Basic usage with success handling
export async function exampleBasicUsage() {
  const request: ClientRequest = {
    type: ClientRequestType.USER_REQUEST_READ,
    sequence: 'example-sequence-1',
  };

  try {
    const response = await sendBizServerRequest(request, { method: AjaxMethod.POST });
    console.log('Success:', response);
    console.log('Request ID:', response.requestId); // Access to requestId property
    return response;
  } catch (error) {
    if (error instanceof BizServerError) {
      console.error('Business Server Error:', {
        message: error.message,
        code: error.code,
        detailCode: error.detailCode,
        serverMessage: error.serverMessage,
        requestId: error.requestId,
      });
    } else {
      console.error('Unexpected error:', error);
    }
    throw error;
  }
}

// Example 2: Using Promise.then/catch syntax
export function examplePromiseChaining() {
  const request: ClientRequest = {
    type: ClientRequestType.BOARD_REQUEST_READ,
    sequence: 'example-sequence-2',
  };

  return sendBizServerRequest(request, { method: AjaxMethod.POST })
    .then((response) => {
      console.log('Success response:', response);
      return response;
    })
    .catch((error: BizServerError) => {
      console.error('Error occurred:', error.message);
      if (error.code) {
        console.error('Server error code:', error.code);
      }
      throw error;
    });
}

// Example 3: Using with blocking requests
export async function exampleBlockingRequest() {
  const request: ClientRequest = {
    type: ClientRequestType.USER_REQUEST_UPDATE,
    sequence: 'example-sequence-3',
  };

  try {
    // This request will block subsequent requests until it completes
    const response = await sendBizServerRequest(request, { 
      method: AjaxMethod.POST, 
      block: true 
    });
    
    console.log('Blocking request completed:', response);
    return response;
  } catch (error) {
    if (error instanceof BizServerError) {
      console.error('Blocking request failed:', error.message);
    }
    throw error;
  }
}

// Example 4: Type-safe error handling
export function exampleTypeSafeErrorHandling(request: ClientRequest): BizRequestPromise {
  const promise = sendBizServerRequest(request, { method: AjaxMethod.POST });
  
  // The promise is typed to return ClientResponse on success
  // and BizServerError on failure
  promise
    .then((response) => {
      // response is typed as ClientResponse
      console.log('Response code:', response.code);
      console.log('Response message:', response.message);
    })
    .catch((error) => {
      // error is typed as BizServerError
      console.error('Error code:', error.code);
      console.error('Detail code:', error.detailCode);
      console.error('Server message:', error.serverMessage);
    });
    
  return promise;
}

// Example 5: Accessing requestId for request tracking
export function exampleRequestTracking() {
  const request: ClientRequest = {
    type: ClientRequestType.GROUP_REQUEST_READ,
    sequence: 'example-sequence-5',
  };

  const promise = sendBizServerRequest(request, { method: AjaxMethod.POST });
  
  // Access requestId immediately for tracking
  console.log('Started request with ID:', promise.requestId);
  
  return promise
    .then((response) => {
      console.log(`Request ${promise.requestId} completed successfully`);
      return response;
    })
    .catch((error: BizServerError) => {
      console.error(`Request ${promise.requestId} failed:`, error.message);
      throw error;
    });
}
