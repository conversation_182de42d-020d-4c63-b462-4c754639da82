# BizRequestPromise 使用指南

## 概述

`BizRequestPromise` 是一个专门为业务服务器请求设计的 Promise 类，它继承自 `RequestPromise<ClientResponse>`，提供了类型安全的请求处理机制。

## 主要特性

1. **类型安全**: 成功时返回 `ClientResponse` 对象，失败时返回 `BizServerError` 对象
2. **请求跟踪**: 每个请求都有唯一的 `requestId` 用于跟踪
3. **阻塞支持**: 支持 `block` 参数来控制请求的执行顺序
4. **错误处理**: 自动将服务器错误转换为 `BizServerError` 对象
5. **继承关系**: 完全兼容标准 Promise API

## 基本用法

### 1. 发送基本请求

```typescript
import { sendBizServerRequest, BizServerError } from './bizServerRequest';
import { ClientRequest, ClientRequestType } from '@moxo/proto';
import { AjaxMethod } from '../network/ajax';

const request: ClientRequest = {
  type: ClientRequestType.USER_REQUEST_READ,
  sequence: 'user-read-001'
};

// 使用 async/await
try {
  const response = await sendBizServerRequest(request, { method: AjaxMethod.POST });
  console.log('请求成功:', response.message);
  console.log('请求ID:', response.requestId);
} catch (error) {
  if (error instanceof BizServerError) {
    console.error('业务错误:', error.message);
    console.error('错误代码:', error.code);
    console.error('请求ID:', error.requestId);
  }
}
```

### 2. 使用 Promise 链式调用

```typescript
sendBizServerRequest(request, { method: AjaxMethod.POST })
  .then(response => {
    console.log('成功:', response);
    return response;
  })
  .catch(error => {
    if (error instanceof BizServerError) {
      console.error('失败:', error.message);
    }
    throw error;
  });
```

### 3. 阻塞请求

```typescript
// 这个请求会阻塞后续请求，直到完成
const blockingRequest = sendBizServerRequest(request, { 
  method: AjaxMethod.POST, 
  block: true 
});

console.log('阻塞请求ID:', blockingRequest.requestId);
```

## 高级用法

### 1. 批量请求处理

```typescript
async function handleBatchRequests() {
  const requests = [
    { type: ClientRequestType.USER_REQUEST_READ, sequence: 'batch-1' },
    { type: ClientRequestType.BOARD_REQUEST_READ, sequence: 'batch-2' },
    { type: ClientRequestType.GROUP_REQUEST_READ, sequence: 'batch-3' }
  ];

  const promises = requests.map(req => 
    sendBizServerRequest(req, { method: AjaxMethod.POST })
  );

  try {
    const responses = await Promise.all(promises);
    console.log('所有请求成功:', responses.length);
    return responses;
  } catch (error) {
    console.error('批量请求失败:', error);
    throw error;
  }
}
```

### 2. 请求重试机制

```typescript
async function requestWithRetry(request: ClientRequest, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const promise = sendBizServerRequest(request, { 
        method: AjaxMethod.POST,
        timeout: 5000 
      });
      
      console.log(`第 ${attempt} 次尝试，请求ID: ${promise.requestId}`);
      return await promise;
      
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}
```

### 3. 使用静态方法

```typescript
// 创建立即解析的 Promise
const successPromise = BizRequestPromise.resolve({
  code: 200,
  message: '成功',
  sequence: 'immediate-success'
}, 'static-success-id');

// 创建立即拒绝的 Promise
const errorPromise = BizRequestPromise.reject(
  new BizServerError('测试错误', 500),
  'static-error-id'
);
```

## 错误处理

### BizServerError 属性

```typescript
interface BizServerError {
  name: string;                    // 'BizServerError'
  message: string;                 // 错误消息
  code?: ClientResponseCode;       // 响应代码
  detailCode?: ClientResponseDetailCode; // 详细错误代码
  serverMessage?: string;          // 服务器返回的错误消息
  response?: ClientResponse;       // 完整的服务器响应
  requestId?: string;              // 请求ID
}
```

### 错误处理最佳实践

```typescript
try {
  const response = await sendBizServerRequest(request, options);
  // 处理成功响应
} catch (error) {
  if (error instanceof BizServerError) {
    // 处理业务错误
    switch (error.code) {
      case ClientResponseCode.RESPONSE_UNAUTHORIZED:
        // 处理未授权错误
        break;
      case ClientResponseCode.RESPONSE_SERVER_ERROR:
        // 处理服务器错误
        break;
      default:
        // 处理其他业务错误
    }
  } else {
    // 处理网络错误或其他异常
    console.error('未知错误:', error);
  }
}
```

## 类型安全

`BizRequestPromise` 提供完整的 TypeScript 类型支持：

```typescript
// response 自动推断为 ClientResponse 类型
const response = await sendBizServerRequest(request, options);
console.log(response.code);     // ✅ 类型安全
console.log(response.message);  // ✅ 类型安全

// error 在 catch 块中需要类型检查
catch (error) {
  if (error instanceof BizServerError) {
    console.log(error.code);      // ✅ 类型安全
    console.log(error.requestId); // ✅ 类型安全
  }
}
```

## 注意事项

1. **请求ID**: 每个请求都有唯一的 `requestId`，可用于请求跟踪和调试
2. **阻塞请求**: 使用 `block: true` 时，后续请求会等待当前请求完成
3. **错误转换**: 所有错误都会被转换为 `BizServerError` 实例
4. **类型检查**: 在处理错误时，建议使用 `instanceof` 进行类型检查

## 测试

运行测试文件来验证功能：

```typescript
import { runAllTests } from './bizServerRequest.test';
runAllTests();
```

## 相关文件

- `bizServerRequest.ts` - 主要实现文件
- `bizServerRequest.types.ts` - 类型定义文件
- `bizServerRequest.example.ts` - 使用示例文件
- `bizServerRequest.test.ts` - 测试文件
