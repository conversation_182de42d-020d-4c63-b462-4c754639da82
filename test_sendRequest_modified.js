// Simple test to verify the modified sendRequest function
// This test uses Node.js and doesn't require TypeScript compilation

const { sendRequest, AjaxMethod } = require('./shared_libraries/sdk/lib/network/ajax');

async function testModifiedSendRequest() {
  console.log('Testing modified sendRequest function...');
  
  try {
    // Test 1: Basic non-blocking request
    console.log('\n1. Testing non-blocking request...');
    const request1 = sendRequest('https://httpbin.org/delay/1', {
      method: AjaxMethod.GET
    });
    
    console.log('Request 1 has requestId:', request1.requestId);
    console.log('Request 1 is instance of Promise:', request1 instanceof Promise);
    
    // Test 2: Blocking request
    console.log('\n2. Testing blocking request...');
    const request2 = sendRequest('https://httpbin.org/delay/1', {
      method: AjaxMethod.GET,
      block: true
    });
    
    console.log('Request 2 has requestId:', request2.requestId);
    console.log('Request 2 is instance of Promise:', request2 instanceof Promise);
    
    // Test 3: Verify requests can be awaited
    console.log('\n3. Testing request execution...');
    const startTime = Date.now();
    
    const result1 = await request1;
    console.log('Request 1 completed in:', Date.now() - startTime, 'ms');
    
    const result2 = await request2;
    console.log('Request 2 completed in:', Date.now() - startTime, 'ms');
    
    console.log('\n✅ All tests passed! Modified sendRequest function works correctly.');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testModifiedSendRequest();
