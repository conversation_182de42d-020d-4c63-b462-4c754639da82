{"private": true, "name": "flutter_web", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev:identity": "cd projects/identity && pnpm dev", "format": "prettier . --write", "format:check": "prettier . --check"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "resolutions": {"vite": "^6.3.5"}, "devDependencies": {"prettier": "^3.6.2", "typescript": "^5.5.3", "@jest/globals": "^29.7.0", "@vitest/coverage-v8": "^2.1.8", "vitest": "^2.1.8"}, "pnpm": {"allowedDeprecatedVersions": {"glob": "7.2.3", "inflight": "1.0.6", "sourcemap-codec": "1.4.8"}, "peerDependencyRules": {"allowedVersions": {"typescript": "5.8.3"}}, "onlyBuiltDependencies": ["core-js", "esbuild", "simple-git-hooks", "unrs-resolver"]}}